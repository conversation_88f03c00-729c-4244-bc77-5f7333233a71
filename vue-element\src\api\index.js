import axios from 'axios'
import { ElMessage } from 'element-plus'

// 创建axios实例
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 60000,  // 🔥 增加到60秒，应对大数据量查询
  headers: {
    'Content-Type': 'application/json'
  }
})

// 创建专门用于消费数据查询的axios实例（中等超时时间）
const consumeApi = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 90000,  // 🔥 消费查询专用：90秒超时
  headers: {
    'Content-Type': 'application/json'
  }
})

// 创建专门用于品智收银查询的axios实例（最长超时时间）
const pinzhiApi = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 120000,  // 🔥 品智查询专用：2分钟超时
  headers: {
    'Content-Type': 'application/json'
  }
})

// 通用请求拦截器配置函数
function setupRequestInterceptor(axiosInstance, instanceName = 'api') {
  return axiosInstance.interceptors.request.use(
    (config) => {
      // 添加认证token
      const token = localStorage.getItem('token')
      if (token) {
        config.headers.Authorization = `Bearer ${token}`
      }

      // 添加请求ID用于追踪
      config.headers['X-Request-ID'] = generateRequestId()

      console.log(`[${instanceName}] 发送请求:`, config.method?.toUpperCase(), config.url, config.data)
      return config
    },
    (error) => {
      console.error(`[${instanceName}] 请求配置错误:`, error)
      return Promise.reject(error)
    }
  )
}

// 为三个实例设置请求拦截器
setupRequestInterceptor(api, 'API')
setupRequestInterceptor(consumeApi, 'CONSUME-API')
setupRequestInterceptor(pinzhiApi, 'PINZHI-API')

// 通用响应拦截器配置函数
function setupResponseInterceptor(axiosInstance, instanceName = 'api') {
  return axiosInstance.interceptors.response.use(
    (response) => {
      const { data } = response

      // 统一处理响应格式
      if (data.code === 200) {
        return data
      } else {
        // 业务错误
        ElMessage.error(data.message || '请求失败')
        return Promise.reject(new Error(data.message || '请求失败'))
      }
    },
    (error) => {
      console.error(`[${instanceName}] 响应错误:`, error)

      // 🔥 特殊处理超时错误
      if (error.code === 'ECONNABORTED' && error.message.includes('timeout')) {
        if (instanceName === 'PINZHI-API') {
          ElMessage.error('品智数据查询超时，数据量较大请耐心等待或稍后重试')
        } else if (instanceName === 'CONSUME-API') {
          ElMessage.error('消费数据查询超时，数据量较大请稍后重试')
        } else {
          ElMessage.error('请求超时，请稍后重试')
        }
        return Promise.reject(error)
      }

      // 网络错误处理
      if (!error.response) {
        ElMessage.error('网络连接失败，请检查网络设置')
        return Promise.reject(error)
      }

      const { status, data } = error.response

      switch (status) {
        case 400:
          ElMessage.error(data?.message || '请求参数错误')
          break
        case 401:
          ElMessage.error('登录已过期，请重新登录')
          // 清除token并跳转到登录页
          localStorage.removeItem('token')
          window.location.href = '/login'
          break
        case 403:
          ElMessage.error('没有权限访问该资源')
          break
        case 404:
          ElMessage.error('请求的资源不存在')
          break
        case 500:
          ElMessage.error('服务器内部错误')
          break
        default:
          ElMessage.error(data?.message || `请求失败 (${status})`)
      }

      return Promise.reject(error)
    }
  )
}

// 为三个实例设置响应拦截器
setupResponseInterceptor(api, 'API')
setupResponseInterceptor(consumeApi, 'CONSUME-API')
setupResponseInterceptor(pinzhiApi, 'PINZHI-API')

// 生成请求ID
function generateRequestId() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

// 会员报表相关API
export const memberReportApi = {
  // 查询周报数据
  getWeekData(params) {
    return api.post('/inquiry/week', params)
  },
  
  // 查询月报数据
  getMonthData(params) {
    return api.post('/inquiry/month', params)
  },
  
  // 查询季报数据
  getQuarterData(params) {
    return api.post('/inquiry/quarter', params)
  },
  
  // 查询半年报数据
  getHalfyearData(params) {
    return api.post('/inquiry/halfyear', params)
  },
  
  // 获取所有会员数据
  getAllMemberData(params) {
    return api.post('/query/data/all', params)
  },
  
  // 获取会员基础数据
  getMemberBaseData(params) {
    return api.post('/query/data/member-base', params)
  },
  
  // 获取会员消费数据 - 🔥 使用专门的消费数据API实例
  getMemberConsumeData(params) {
    console.log('🔥 使用消费数据专用API实例，超时时间：90秒')
    return consumeApi.post('/query/data/member-consume', params)
  },
  
  // 获取会员充值数据
  getMemberChargeData(params) {
    return api.post('/query/data/member-charge', params)
  },

  // 获取品智收银数据 - 🔥 使用专门的长超时实例
  getPinzhiCashierData(params) {
    console.log('🔥 使用品智专用API实例，超时时间：2分钟')
    return pinzhiApi.post('/query/data/pinzhi-cashier', params)
  },

  // 获取品智收银AI分析 - 🔥 使用专门的长超时实例
  getPinzhiCashierAIAnalysis(params) {
    console.log('🔥 使用品智专用API实例进行AI分析')
    return pinzhiApi.post('/query/data/pinzhi-cashier-ai-analysis', params)
  },
  
  // 获取券交易数据
  getCouponTradeData(params) {
    return api.post('/query/data/coupon-trade', params)
  },
  
  // 获取券交易AI分析
  getCouponTradeAIAnalysis(params) {
    return api.post('/query/data/coupon-trade/ai-analysis', params)
  },
  
  // 获取会员基础AI分析
  getMemberBaseAIAnalysis(params) {
    return api.post('/query/data/member-base/ai-analysis', params)
  },
  
  // 获取会员消费AI分析 - 🔥 使用专门的消费数据API实例
  getMemberConsumeAIAnalysis(params) {
    console.log('🔥 使用消费数据专用API实例进行AI分析')
    return consumeApi.post('/query/data/member-consume/ai-analysis', params)
  },
  
  // 获取会员充值AI分析
  getMemberChargeAIAnalysis(params) {
    return api.post('/query/data/member-charge/ai-analysis', params)
  },
  
  // 导出报表
  exportReport(params) {
    return api.post('/report/export', params, {
      responseType: 'blob'
    })
  },
  
  // 获取品牌配置
  getBrandConfig(brandId) {
    return api.get(`/brand/${brandId}/config`)
  },
  
  // 获取数据字段配置
  getFieldsConfig(brandId) {
    return api.get(`/fields/config/${brandId}`)
  },

  // 根据SID查询数据
  getDataBySid(params) {
    return api.post('/inquiry/sid', params)
  },

  // 批量SID查询
  getBatchDataBySid(params) {
    return api.post('/inquiry/sid/batch', params)
  },

  // SID数据验证
  validateSid(sid) {
    return api.get(`/inquiry/sid/${sid}/validate`)
  }
}

// 通用API方法
export const commonApi = {
  // 上传文件
  uploadFile(file, onProgress) {
    const formData = new FormData()
    formData.append('file', file)
    
    return api.post('/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress) {
          const percentCompleted = Math.round(
            (progressEvent.loaded * 100) / progressEvent.total
          )
          onProgress(percentCompleted)
        }
      }
    })
  },
  
  // 下载文件
  downloadFile(url, filename) {
    return api.get(url, {
      responseType: 'blob'
    }).then(response => {
      const blob = new Blob([response.data])
      const downloadUrl = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = downloadUrl
      link.download = filename || 'download'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(downloadUrl)
    })
  }
}

// 导出axios实例供其他地方使用
export default api
